import Layout from '@/views/pc-portal/Layout.vue';
import Error404 from '@/views/error/ErrorPage.vue';
import { RouteRecordRaw } from 'vue-router';

export default [
  {
    path: '/',
    name: 'pcPortalTopRoute',
    component: Layout,
    children: [
      {
        path: '',
        name: 'pcPortalHomeIndex',
        component: () => import('@/views/pc-portal/home/<USER>'),
      },
      // 搜索
      {
        path: 'search',
        name: 'pcPortalSearch',
        component: () => import('@/views/pc-portal/search/index.vue'),
      },
      // 学会动态
      {
        path: 'news',
        name: 'pcPortalLearnDynamicIndex',
        component: () => import('@/views/pc-portal/learndynamic/index.vue'),
      },
      {
        path: 'news/detail',
        name: 'pcPortalLearnDynamicDetails',
        component: () => import('@/views/pc-portal/learndynamic/comp/details.vue'),
      },
      // 通知公告
      {
        path: 'notice',
        name: 'pcPortalNoticeIndex',
        component: () => import('@/views/pc-portal/notice/index.vue'),
      },
      {
        path: 'notice/detail',
        name: 'pcPortalNoticeDetails',
        component: () => import('@/views/pc-portal/notice/comp/details.vue'),
      },
      // 学会概况
      {
        path: 'overview',
        name: 'pcPortalOverviewIntroduction',
        component: () => import('@/views/pc-portal/overView/index.vue'),
      },
      // 会议活动
      // 会员中心
      // 分支机构
      // 联系我们
      {
        path: 'contact',
        name: 'pcPortalContact',
        component: () => import('@/views/pc-portal/contact/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    name: 'pcPortalLogin',
    component: () => import('@/views/pc-portal/login/index.vue'),
  },
  {
    path: '/:catchAll(.*)*',
    name: 'ERROR_404',
    component: Error404,
  },
] as RouteRecordRaw[];
