<template>
  <div :class="$style.organizationLeadershipPage">
    <n-spin :show="loading">
      <div v-if="leadersData && leadersData.length > 0">
        <h2 :class="$style.title">理事会领导</h2>

        <!-- 领导成员网格 -->
        <div :class="$style.leadersGrid">
          <div v-for="(leader, index) in leadersData" :key="leader.id" :class="$style.leaderCard">
            <!-- 头像 -->
            <div :class="$style.avatar">
              <img :src="getImg(index)" :alt="leader.name" />
            </div>

            <div :class="$style.info">
              <div :class="$style.name">{{ leader.ldxm }}</div>
              <div :class="$style.position">{{ leader.ldzw }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>空</div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { showLeaderListAPI } from '../fetchData';
import { Response, PortalOrgLeaderPageVo } from '../type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const leadersData = ref<PortalOrgLeaderPageVo[]>([]);
const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(getLeader);

function getLeader() {
  search(
    showLeaderListAPI().then((res: Response) => {
      leadersData.value = res.data || [];
      updateTotal(0);
    })
  );
}

// 获取头像
function getImg(index: number) {
  let filePath = leadersData.value[index].fileAttachmentList || [];
  const img = window.$SYS_CFG.apiBaseFile + filePath[0].filePath;
  return img;
}

onMounted(() => {
  getLeader();
});

defineOptions({ name: 'OrganizationLeadershipIndex' });
</script>

<style module lang="scss">
.organizationLeadershipPage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 2rem;
    text-align: left;
  }

  .leadersGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 28px;

    .leaderCard {
      width: 448px;
      height: 102px;
      padding-left: 25px;
      background: #fff;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      display: flex;
      align-items: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      }
      .avatar {
        width: 54px;
        height: 54px;
        border-radius: 50%;
        overflow: hidden;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 21px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .info {
        .name {
          font-size: 18px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 0.5rem;
        }
        .position {
          font-size: 14px;
          color: #2196f3;
          font-weight: 500;
          float: left;
        }
      }
    }
  }
}
</style>
