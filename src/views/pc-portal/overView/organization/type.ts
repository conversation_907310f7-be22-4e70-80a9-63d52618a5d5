/**
 * ResultModelListPortalOrgLeaderPageVo
 */
export interface Response {
  /**
   * 状态码
   * 状态码,200正常
   */
  code?: number;
  /**
   * 数据
   * 返回数据
   */
  data?: PortalOrgLeaderPageVo[];
  /**
   * 消息
   * 返回提示
   */
  message?: string;
  [property: string]: any;
}

/**
 * 学会领导
 *
 * PortalOrgLeaderPageVo
 */
export interface PortalOrgLeaderPageVo {
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 图片文件列表
   */
  fileAttachmentList?: FileAttachment[];
  /**
   * 主键
   */
  id?: string;
  /**
   * 领导图像
   */
  ldtpFileId?: string;
  /**
   * 领导姓名
   */
  ldxm?: string;
  /**
   * 领导职务
   */
  ldzw?: string;
  /**
   * 排序
   */
  px?: number;
  /**
   * 审核人
   */
  shrName?: string;
  [property: string]: any;
}

/**
 * 附件表
 *
 * FileAttachment
 */
export interface FileAttachment {
  /**
   * 业务数据
   */
  businessData?: string;
  /**
   * 业务ID
   */
  businessId?: string;
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 文件名称
   */
  fileName?: string;
  /**
   * 文件存放路径
   */
  filePath?: string;
  /**
   * 文件大小
   */
  fileSize?: string;
  /**
   * ID
   * 主键
   */
  id?: string;
  /**
   * 是否删除：0-未删除 1-已删除
   */
  isDel?: string;
  /**
   * 文件后缀
   */
  suffix?: string;
  /**
   * 上传日期
   */
  uploadDate?: string;
  /**
   * 视频时长
   */
  videoTime?: number;
  [property: string]: any;
}
