<template>
  <div :class="$style.introductionPage">
    <n-spin :show="loading">
      <!-- 学会简介内容 -->
      <IntroductionContent :introduction-data="introductionData" />

      <!-- 发展历程内容 -->
      <DevelopmentHistory :history-data="introductionData" />
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import IntroductionContent from './comp/IntroductionContent.vue';
import DevelopmentHistory from './comp/DevelopmentHistory.vue';
import { showIntroductionContent } from './fetchData';
import type { Response, PortalIntroductionVo } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const introductionData = ref<PortalIntroductionVo>({});

const [loading, search] = useAutoLoading(true);
/**
 * 获取学会简介内容
 */
const getContent = async () => {
  search(
    showIntroductionContent().then((res: Response) => {
      introductionData.value = res.data || [];
      updateTotal(res.data?.total || 0);
    })
  );
};
const { updateTotal } = useNaivePagination(getContent);

onMounted(() => {
  getContent();
});

defineOptions({ name: 'IntroductionIndex' });
</script>

<style module lang="scss">
.introductionPage {
  width: 100%;
  // min-height: 600px;
}
</style>
