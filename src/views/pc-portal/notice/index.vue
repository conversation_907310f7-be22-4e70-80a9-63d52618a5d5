<template>
  <div style="width: 100%">
    <div :class="$style.learndynamicheaderWrap">
      <div class="flex items-center justify-between h-[64px] w-[87.5rem] mx-auto">
        <div class="text-[#0C87E5FF] text-[20px] font-semibold">公共安全科学技术学会</div>
        <div>
          <n-tabs type="bar" @update:value="handleUpdateValue" v-model:value="tabName">
            <n-tab v-for="tab in tabs" :key="tab.name" :name="tab.name"> {{ tab.label }} </n-tab>
          </n-tabs>
        </div>
      </div>
      <div class="w-[87.5rem] mx-auto"><ComBread :data="breadData"></ComBread></div>
    </div>
    <div class="com-pc-portal-container">
      <ListIndex ref="tableRef"></ListIndex>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import ListIndex from './comp/ListIndex.vue';
import { queryDictDataByTypeAPI } from '../learndynamic/fetchData';

const tabName = ref('0');
const tableRef = ref();

const tabs = ref<any[]>([]);
const breadData = ref<any>([{ name: '通知公告' }]);
function getTypeList() {
  queryDictDataByTypeAPI({ type: 'tzlx' }).then((res) => {
    console.log(res, 'p[p[p[pp]]]');
    tabs.value = res.data.map((item: any) => ({
      name: item.dictValue,
      label: item.dictLabel,
    }));
    breadData.value = [
      { name: '学会动态' },
      {
        name: tabs.value[+tabName.value].label,
      },
    ];
    tableRef.value.getTableDataWrap({ xslx: tabName.value });
  });
}
onMounted(() => {
  getTypeList();
});

function handleUpdateValue(val: string) {
  tabName.value = val;
  breadData.value = [
    { name: '通知公告' },
    {
      name: tabs.value[+tabName.value].label,
    },
  ];
  tableRef.value.getTableDataWrap({ xslx: tabName.value });
}

defineOptions({ name: 'NoticeIndex' });
</script>

<style module lang="scss">
.learndynamicheaderWrap {
  width: 100%;
  height: 64px;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 0px 0px;
}
</style>
