<template>
  <n-carousel style="height: 343px" :space-between="20" draggable>
    <n-carousel-item v-for="(page, index) in paginatedData" :key="index">
      <div class="list">
        <div class="li" v-for="item in page" :key="item.id">
          {{ item.name }}
        </div>
      </div>
    </n-carousel-item>
  </n-carousel>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCarousel, NCarouselItem } from 'naive-ui';

const tableData = ref([
  { id: 1, name: '应急管理专业委员会' },
  { id: 2, name: '爆炸安全与防护专业委员会' },
  { id: 3, name: '石油与化工安全专业委员会' },
  { id: 4, name: '检测认证工作委员会' },
  { id: 5, name: '消防分会' },
  { id: 6, name: '灾害医学专业委员会' },
  { id: 7, name: '应急救援专业委员会' },
  { id: 8, name: '灾害事故调查评估专业委员会' },
  { id: 9, name: '学科建设工作委员会' },
  { id: 10, name: '航空安全专业委员会' },
  { id: 11, name: '矿业与地下工程安全专业委员会' },
  { id: 12, name: '安全文化与应急科普工作委员会' },
  { id: 13, name: '分支机构13' },
  { id: 14, name: '分支机构14' },
  { id: 13, name: '分支机构13' },
  { id: 14, name: '分支机构14' },
  { id: 13, name: '分支机构13' },
  { id: 14, name: '分支机构14' },
  { id: 13, name: '分支机构13' },
  { id: 14, name: '分支机构14' },
]);

// 分页逻辑：每页12条
const paginatedData = computed(() => {
  const pageSize = 12;
  const pages = [];
  for (let i = 0; i < tableData.value.length; i += pageSize) {
    pages.push(tableData.value.slice(i, i + pageSize));
  }
  return pages;
});

const effectRef = ref<'slide' | 'fade' | 'card'>('slide');
const isCardRef = computed(() => effectRef.value === 'card');
defineOptions({ name: 'contentBox6Index' });
</script>

<style scoped lang="scss">
.list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  .li {
    width: 24%;
    height: 56px;
    line-height: 56px;
    text-align: center;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #d8e4f0;
  }
}
// .carousel-img {
//   margin: 0 auto;
//   width: 100%;
//   height: 100%;
//   object-fit: cover;
// }
:deep(.n-carousel) {
  .n-carousel__dot .n-carousel__dot--active {
    background: red !important;
  }
}
</style>
