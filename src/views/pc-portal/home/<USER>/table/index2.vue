<template>
  <div class="list">
    <n-scrollbar style="height: calc(478px - 206px)">
      <div class="li" v-for="item in tableData" :key="item.id">{{ item.name }}</div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tableData = ref([
  { id: 1, name: '教育部' },
  {
    id: 2,
    name: '科技部',
  },
  {
    id: 3,
    name: '民政部',
  },
  {
    id: 4,
    name: '住建部',
  },
  {
    id: 5,
    name: '应急管理部',
  },
  {
    id: 5,
    name: '应急管理部',
  },
  {
    id: 5,
    name: '应急管理部',
  },
]);
defineOptions({ name: 'contentBox5TableIndex' });
</script>

<style scoped lang="scss">
.list {
  width: 100%;

  // display: flex;
  // justify-content: space-around;
  .li {
    // height: 80px;
    width: 98%;
    padding-left: 10px;
    padding-right: 10px;
    height: 50px;
    margin-bottom: 20px;
    line-height: 50px;
    border-radius: 6px;
    background-color: rgb(226, 237, 254);
    color: rgb(47, 148, 232);
  }
}
</style>
