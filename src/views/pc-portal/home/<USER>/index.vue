<template>
  <ComPortalBox title="资料下载" :hasMore="false">
    <div class="contentBox4">
      <div
        v-for="item in listData"
        :key="item.id"
        @click="handleClick(item)"
        :class="item.lx == selectItme ? 'contentBox4_list' : 'contentBox4_list2'"
      >
        <img :src="item.img" alt="动态图片" />
        <p>{{ item.title }}</p>
        <p>{{ item.content }}</p>
      </div>
    </div>
  </ComPortalBox>
  <n-modal
    v-model:show="showModal"
    :style="{ width: '700px', height: '600px' }"
    class="custom-card"
    preset="card"
    title="资料下载"
    :bordered="false"
  >
    <n-infinite-scroll v-if="tableData.length > 0" style="height: 500px" :distance="400" @load="handleLoad">
      <div class="modalContent" v-for="item in tableData" :key="item.id">
        <div class="text-[#000000FF] text-[16px] font-[600]">{{ item.bt }}</div>
        <div>
          <n-a :href="getPahtUrl(item.tzfjFileAttachmentList[0].filePath)" target="_blank" download>资料下载</n-a>
        </div>
      </div>
      <div v-if="loading" class="text-center">加载中...</div>
      <div v-if="noMore && !loading" class="text-center">没有更多了</div>
    </n-infinite-scroll>
    <ComEmpty v-else></ComEmpty>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import zl1 from '../assets/ziliao1.png';
import zl2 from '../assets/ziliao2.png';
import zl3 from '../assets/ziliao3.png';
import zl4 from '../assets/ziliao4.png';
import { useNaivePagination } from '@/utils/useNaivePagination';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { showDocumentsAPI } from './fetchData';
import ComEmpty from '@/components/empty/index.vue';

const { pagination, updateTotal } = useNaivePagination(getTableData);
pagination.pageSize = 10;
const listData = ref([
  {
    id: 1,
    title: '会员申请资料',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    lx: '2',
    img: zl1,
  },
  {
    id: 2,
    title: '研究报告',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    lx: '1',
    img: zl2,
  },
  {
    id: 3,
    title: '学术期刊',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    lx: '0',
    img: zl3,
  },
  {
    id: 4,
    title: '培训资料',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    lx: '3',
    img: zl4,
  },
]);
const selectItme = ref('');
const showModal = ref(false);

const [loading, search] = useAutoLoading(false);
const tableData = ref<any>([]);

const isLoading = ref(false); // 添加加载状态
const lastLoadTime = ref(0); // 记录上次加载时间
function handleClick(item: any) {
  selectItme.value = item.lx;
  showModal.value = true;
  tableData.value = [];
  pagination.page = 1;
  updateTotal(0);
  getTableData();
}
const noMore = computed(() => tableData.value.length >= (pagination.itemCount || 0));
// 修改 getTableData 方法以支持分页加载
async function getTableData() {
  // 防止重复请求
  if (isLoading.value) return;
  isLoading.value = true;
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    xslx: selectItme.value,
  };

  try {
    const res: any = await search(showDocumentsAPI(params));
    // 累加数据而不是覆盖
    tableData.value = [...tableData.value, ...(res.data.rows || [])];
    updateTotal(res.data.total || 0);
  } catch (error) {
    console.error('加载失败:', error);
    if (pagination.page && pagination.page > 1) {
      pagination.page -= 1;
    }
  } finally {
    isLoading.value = false;
    lastLoadTime.value = Date.now(); // 更新最后加载时间
  }
}

// 新增 handleLoad 方法用于无限滚动
function handleLoad() {
  const now = Date.now();
  // 如果距离上次加载不足300ms，则不执行
  if (now - lastLoadTime.value < 500) {
    return;
  }
  if (noMore.value || isLoading.value) return;
  if (pagination.page !== undefined) {
    pagination.page += 1;
  } else {
    pagination.page = 1;
  }

  getTableData();
}
// 地址
function getPahtUrl(val: string) {
  return window.$SYS_CFG.apiBaseFile + val;
}

defineOptions({ name: 'contentBox4Index' });
</script>

<style scoped lang="scss">
.contentBox4 {
  width: 100%;
  height: 252px;
  display: flex;
  justify-content: space-between;
  .contentBox4_list {
    width: 23%;
    height: 100%;
    border-radius: 8px;
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: 112px;
      height: 112px;
    }
    p {
      text-align: center;
    }
    p:nth-child(2) {
      font-weight: 600;
      font-size: 24px;
      color: #07417b;
    }
    p:nth-child(3) {
      width: 75%;
      margin-top: 10px;
      font-weight: 400;
      font-size: 14px;
      color: #505559;
    }
  }
  .contentBox4_list2 {
    width: 23%;
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(207, 230, 252, 0.6) 0%, rgba(219, 236, 255, 0.6) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: 112px;
      height: 112px;
    }
    p {
      text-align: center;
    }
    p:nth-child(2) {
      font-weight: 600;
      font-size: 24px;
      color: #07417b;
    }
    p:nth-child(3) {
      width: 75%;
      margin-top: 10px;
      font-weight: 400;
      font-size: 14px;
      color: #505559;
    }
  }
}
.modalContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 62px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ebeef5;
  padding: 20px 24px;
  margin: 10px;
}
</style>
