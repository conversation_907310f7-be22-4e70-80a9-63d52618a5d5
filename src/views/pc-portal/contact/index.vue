<template>
  <div :class="$style.pcPortalContact" class="com-pc-portal-container">
    <ComBread :data="breadData" />

    <div :class="$style.title">联系我们</div>

    <iframe :class="$style.map" src="./map.html" frameborder="0" allowtransparency></iframe>

    <table>
      <tr>
        <td>地 址：北京市海淀区清华大学吕大龙楼213-2</td>
        <td>邮 编：100084</td>
      </tr>
      <tr>
        <td>网 址：www.publicsafety.org.cn</td>
        <td>邮 箱：<EMAIL></td>
      </tr>
      <tr>
        <td>电 话：010-62793121</td>
        <td>传 真：010-62792863</td>
      </tr>
      <tr>
        <td>银行户名：公共安全科学技术学会</td>
        <td>开户行：中国银行北京清华园支行</td>
      </tr>
      <tr>
        <td>银行帐号：341561636295</td>
      </tr>
    </table>
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import type { IBreadData } from '@/types';

const breadData: IBreadData[] = [{ name: '首页', routeRaw: { path: '/' }, clickable: true }, { name: '联系我们' }];

defineOptions({ name: 'pcPortalContact' });
</script>

<style module lang="scss">
.pcPortalContact {
  padding: 40px;

  .title {
    font-size: 32px;
    font-weight: bolder;
    text-align: center;
    margin: 30px 0;
  }

  .map {
    width: 100%;
    height: 62vh;
  }

  table {
    margin: 35px 0;
  }

  td {
    color: #1a1a1a;
    font-size: 16px;
    line-height: 1.5;
    margin: 10px 0;
  }
}
</style>
