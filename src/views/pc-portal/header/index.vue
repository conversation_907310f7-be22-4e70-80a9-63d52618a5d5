<template>
  <header :class="$style.PcPortalHeader">
    <div>
      <img src="./assets/logo.svg" alt="" class="w-[72px] select-none" />
      <img src="./assets/title.svg" alt="公共安全科学技术学会" class="w-[217px] ml-[20px]" />

      <div :class="$style.search">
        <n-input-group>
          <n-input
            v-model:value="keyword"
            status="warning"
            size="large"
            placeholder="请输入想要搜索的内容"
            maxlength="50"
            round
            clearable
          />
          <n-button type="warning" size="large" style="--n-padding: 0 15px" round @click="handleSearch">
            <template #icon><IconSearch /></template>
            搜索
          </n-button>
        </n-input-group>
      </div>
    </div>
    <div>
      <div :class="$style.menuGroup">
        <n-menu
          v-model:value="activeKey"
          mode="horizontal"
          :options="menuOptions"
          :theme-overrides="themeOverrides"
          @update-value="handleMenuClick"
        />

        <n-button type="primary" disabled @click="handleLogin">会员登录/注册</n-button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { MenuOption, MenuProps } from 'naive-ui';
import { useRouter } from 'vue-router';
import { BySearch as IconSearch } from '@kalimahapps/vue-icons';

const themeOverrides: NonNullable<MenuProps['themeOverrides']> = {
  itemHeight: '72px',
  itemTextColorHorizontal: '#212121',
  itemTextColorHoverHorizontal: '#222',
  itemTextColorActiveHorizontal: '#fff',
  itemTextColorActiveHoverHorizontal: '#fff',
};

/**
 * 菜单
 * link 支持 路由path 以及 http链接
 */
const menuOptions: MenuOption[] = [
  { key: 'sy', label: '首页', link: '/' },
  { key: 'xhgk', label: '学会概况', link: '/overview' },
  { key: 'dhgz', label: '党建工作', link: '', disabled: true },
  { key: 'xhdt', label: '学会动态', link: '/news' },
  { key: 'hyhd', label: '会议活动', link: '', disabled: true },
  { key: 'hycz', label: '会员中心', link: '', disabled: true },
  { key: 'zzjg', label: '分支机构', link: '' },
  { key: 'tzgg', label: '通知公告', link: '/notice' },
  { key: 'lxwm', label: '联系我们', link: '/contact' },
];

const router = useRouter();
const activeKey = ref();
const keyword = ref(router.currentRoute.value.query.keyword || '');

function handleSearch() {
  router.push({ name: 'pcPortalSearch', query: { keyword: keyword.value, timestamp: +new Date() } });
}

function handleMenuClick(key: string, item: MenuOption) {
  const link = (item.link || '') as string;

  if (link) {
    if (link.startsWith('http')) {
      window.open(link, link);
    } else {
      router.push({ path: link });
    }
  }
}

function handleLogin() {
  // todo
}

function init() {
  let curMenuOpt = menuOptions[0];

  for (const item of menuOptions.slice(1)) {
    const routePath = (router.currentRoute.value.path || router.currentRoute.value.meta.parentPath || '') as string;
    const link = item.link as string;

    if (link && routePath.startsWith(link)) {
      curMenuOpt = item;
      break;
    }
  }

  // 当前激活的菜单
  activeKey.value = curMenuOpt.key;
}

watch(() => router.currentRoute.value, init, { immediate: true });

defineOptions({ name: 'PcPortalHeader' });
</script>

<style module lang="scss">
.PcPortalHeader {
  > div:nth-child(1) {
    position: relative;
    width: var(--com-pc-portal-content-width);
    height: 108px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    pointer-events: none;
    background: url('./assets/bg.svg') -260px bottom no-repeat;
    background-size: 1920px 108px;
  }

  > div:nth-child(2) {
    height: 72px;
    background: url('./assets/bg-menu.svg') center center no-repeat;
    background-size: cover;
  }

  .menuGroup {
    width: var(--com-pc-portal-content-width) !important;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :global(.n-menu.n-menu--horizontal .n-menu-item-content) {
    min-width: 100px;
    text-align: center;
  }

  :global(.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected) {
    background: var(--com-pc-portal-primary-color);
  }

  .search {
    pointer-events: auto;
    position: absolute;
    bottom: 24px;
    right: 0;
    width: 372px;
  }
}
</style>
