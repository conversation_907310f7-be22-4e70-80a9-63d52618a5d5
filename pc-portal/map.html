<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
        }
        .map {
            width: 100vw;
            height: 100vh;
        }
    </style>

    <script src="http://api.map.baidu.com/api?v=2.0&ak=xazbmUaVv733N7KulDttkM1n" type="text/javascript"></script>
    <script type="text/javascript">
        window.onload = function () {
            // 百度地图API功能
            var map = new BMap.Map("JS_Map");
            var point = new BMap.Point(40.003422, 116.335808);
            map.centerAndZoom(point, 12);
            // 创建地址解析器实例
            var myGeo = new BMap.Geocoder();
            // 将地址解析结果显示在地图上,并调整地图视野
            myGeo.getPoint("北京市海淀区清华大学刘卿楼1001", function (point) {
                if (point) {
                    map.centerAndZoom(point, 16);
                    map.addOverlay(new BMap.Marker(point));
                    map.enableScrollWheelZoom(true);
                } else {
                    alert("您选择地址没有解析到结果!");
                }
            }, "北京市");
        }
    </script>
</head>
<body>
<div id="JS_Map" class="map"></div>
</body>
</html>